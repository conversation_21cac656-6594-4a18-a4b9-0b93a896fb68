/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-26 10:10:35
 * @Description: 
 * @LastEditTime: 2025-07-28 09:31:58
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import React from "react";
import {CSSUtils, ThemeConfig, ThemeProvider} from "@/ui-framework";
import {ConfigProvider} from "antd";
import zhCN from "antd/es/locale/zh_CN";
import RoutesProvider from "@/framework/Routes/RoutesProvider";
// 组件注册
import "@/config/register";

export const theme = {
    token: {
        colorPrimary: CSSUtils.getRootVariable('--primary-color'),
        contentFontSize: CSSUtils.getRootVariable('--content-font-size'),
    }
} as ThemeConfig;

const App = () => {
 
    return (
        <ThemeProvider theme={theme}>
            <ConfigProvider
                locale={zhCN}
                theme={theme}
            >
                <RoutesProvider/>
            </ConfigProvider>
        </ThemeProvider>
    )
}

export default App;
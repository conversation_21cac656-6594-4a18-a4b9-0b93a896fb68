import {ActionType, ProColumns} from "@ant-design/pro-components";
import React from "react";
import {useEnforcementNumberPresenter} from "@/pages/enforcement/enforcementNumber/hooks/usePresenter";
import HRBody from "@/components/LayoutPage";
import {HomeOutlined} from "@ant-design/icons";
import {EnforcementNumberData} from "@/pages/enforcement/enforcementNumber/types";
import Page from "@/components/LayoutPage";
import HRHeader from "@/components/Header";
import HRCard from "@/components/LayoutCard";
import {ProTable} from "@/components-pc";

const EnforcementNumberManagePage: React.FC = () => {

    const actionRef = React.useRef<ActionType>(null);
    // 通过hooks获取state状态数据与presenter对象
    const {state, presenter} = useEnforcementNumberPresenter();

    const columns: ProColumns<EnforcementNumberData>[] = [
        {
            title: "编号",
            dataIndex: "id",
            search: false,
            width: 80,
        },
        {
            title: "号段ID",
            dataIndex: "segmentId",
            search: false,
            width: 100,
            hidden: true,
        },
        {
            title: "单位ID",
            dataIndex: "unitId",
            search: false,
            width: 100,
            hidden: true,
        },
        {
            title: "单位名称",
            dataIndex: "unitName",
            valueType: "select",
            valueEnum: state.unitOptions.reduce((acc: any, item: any) => {
                acc[item.value] = { text: item.label };
                return acc;
            }, {}),
            fieldProps: {
                placeholder: "请选择单位",
                showSearch: true,
                filterOption: (input: string, option: any) =>
                    option?.label?.toLowerCase().includes(input.toLowerCase())
            },
        },
        {
            title: "执法编号",
            dataIndex: "number",
            fieldProps: {
                placeholder: "请输入执法编号",
            },
        },
        {
            title: "状态",
            dataIndex: "status",
            valueType: "select",
            valueEnum: {
                "DISABLED": {text: "未启用", status: "Default"},
                "UNUSED": {text: "未使用", status: "Processing"},
                "IN_USE": {text: "使用中", status: "Success"},
                "REVOKED": {text: "作废", status: "Error"},
            },
            fieldProps: {
                placeholder: "请选择状态",
            },
        },
        {
            title: "执法人员ID",
            dataIndex: "enforcerId",
            search: false,
            width: 120,
            hidden: true,
        },
        {
            title: "执法人员",
            dataIndex: "enforcerName",
            render: (_, record) => {
                return record.enforcerName || "待分配";
            },
            fieldProps: {
                placeholder: "请输入执法人员姓名",
            },
        },
        {
            title: "备注",
            dataIndex: "remark",
            search: false,
            ellipsis: true,
            width: 150,
        },
        {
            title: "创建人",
            dataIndex: "sysCreator",
            search: false,
            width: 100,
        },
        {
            title: "创建时间",
            dataIndex: "sysCreateTime",
            valueType: "dateTime",
            search: false,
            render: (_, record) => {
                return new Date(record.sysCreateTime).toLocaleString();
            },
        },
    ];
    return (
        <Page>
            <HRBody>
                <HRHeader
                    title={"执法编号管理"}
                    icon={<HomeOutlined/>}
                />
            </HRBody>
            <HRCard>
                <ProTable
                    key={JSON.stringify(state.formInitialValues)}
                    columns={columns}
                    actionRef={actionRef}
                    rowKey={"id"}
                    form={{
                        initialValues: state.formInitialValues
                    }}
                    request={async (params, sort, filter) => {
                        const result = await presenter.searchEnforcementNumberTable(params, sort, filter);
                        return {
                            data: result.data || [],
                            success: result.success,
                            total: result.total || 0,
                        };
                    }}
                />
            </HRCard>
        </Page>
    );
}
export default EnforcementNumberManagePage;
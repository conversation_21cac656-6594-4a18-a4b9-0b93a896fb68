import {EnforcementNumberApi, EnforcementNumberData, UnitOption, StatusOption} from "@/pages/enforcementNumber/types";
import {getNumberPage, getNumberDetail} from "@/api/enforcementNumber/number";
import {httpClient} from "@/api";

/**
 * Model层接口实现
 * EnforcementNumberApiImpl 实现了EnforcementNumberApi接口，提供了数据的增删改查操作
 */
export class EnforcementNumberApiImpl implements EnforcementNumberApi {

    async loadEnforcementNumberTable(params: any, sort: any, filter: any): Promise<{
        data: EnforcementNumberData[];
        success: boolean;
        total: number;
    }> {
        try {
            const response = await getNumberPage(params, sort, filter,[])

            if (response.success) {
                return {
                    data: response.data || [],
                    success: true,
                    total: response.total || 0
                };
            } else {
                return {
                    data: [],
                    success: false,
                    total: 0
                };
            }
        } catch (error) {
            return {
                data: [],
                success: false,
                total: 0
            };
        }
    }


    async getUnitOptions(): Promise<{ success: boolean; data: UnitOption[];}> {
        try {
            // 这里可以调用实际的单位选项API，目前使用模拟数据
            const mockUnitOptions: UnitOption[] = [
                {label: "市公安局", value: "市公安局", code: "SGAJ"},
                {label: "交警支队", value: "交警支队", code: "JJZD"},
                {label: "消防支队", value: "消防支队", code: "XFZD"},
                {label: "城管局", value: "城管局", code: "CGJ"},
                {label: "环保局", value: "环保局", code: "HBJ"}
            ];

            return {
                success: true,
                data: mockUnitOptions
            };
        } catch (error) {
            console.error('获取单位选项失败:', error);
            return {
                success: false,
                data: []
            };
        }
    }

    async getStatusOptions(): Promise<{ success: boolean; data: StatusOption[];}> {
        try {
            const statusOptions: StatusOption[] = [
                { label: "未启用", value: "DISABLED" },
                { label: "未使用", value: "UNUSED" },
                { label: "使用中", value: "IN_USE" },
                { label: "作废", value: "REVOKED" }
            ];

            return {
                success: true,
                data: statusOptions
            };
        }catch (error){
            return {
                success: false,
                data: []
            };
        }
    }
}
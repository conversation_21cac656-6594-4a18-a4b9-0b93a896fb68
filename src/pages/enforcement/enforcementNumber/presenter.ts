import {EnforcementNumberApi, EnforcementNumberState, Dispatch,} from "@/pages/enforcement/enforcementNumber/types";
import {EnforcementNumberApiImpl} from "@/pages/enforcement/enforcementNumber/model";

export class EnforcementNumberPresenter {

    private readonly dispatch: Dispatch<EnforcementNumberState>;
    private readonly api: EnforcementNumberApi;
    private state: EnforcementNumberState;

    public constructor(dispatch: Dispatch<EnforcementNumberState>, api: EnforcementNumberApiImpl, state: EnforcementNumberState) {
        this.dispatch = dispatch;
        this.api = api;
        this.state = state;
    }

    public syncState = (newStatus: EnforcementNumberState) => {
        this.state = newStatus;
    }

    // 从Hash路由URL参数初始化表单
    public initializeFromUrlParams = () => {
        // 处理Hash路由的参数解析
        let searchString = '';
        if (window.location.hash.includes('?')) {
            // 从hash中提取查询参数部分
            searchString = window.location.hash.split('?')[1];
        } else {
            // 如果没有hash，使用传统的search
            searchString = window.location.search.substring(1);
        }

        const urlParams = new URLSearchParams(searchString);
        const urlQueryParams: any = {};

        // 提取URL中的查询参数
        const unitName = urlParams.get('unitName');
        const status = urlParams.get('status');
        const segmentId = urlParams.get('segmentId');

        if (unitName) {
            urlQueryParams.unitName = unitName;
        }
        if (status) {
            urlQueryParams.status = status;
        }
        if (segmentId) {
            urlQueryParams.segmentId = segmentId;
        }

        // 更新state中的表单初始值
        this.dispatch((prevState) => ({
            ...prevState,
            formInitialValues: urlQueryParams
        }));

        // 同时加载单位选项
        this.loadUnitOptions();
    }

    // 执法编号表格数据加载函数，支持URL参数
    public searchEnforcementNumberTable = (params: any, sort: any, filter: any) => {
        // 处理Hash路由的参数解析
        let searchString = '';
        if (window.location.hash.includes('?')) {
            searchString = window.location.hash.split('?')[1];
        } else {
            searchString = window.location.search.substring(1);
        }

        const urlParams = new URLSearchParams(searchString);
        const urlQueryParams: any = {};

        // 提取URL中的查询参数
        const unitName = urlParams.get('unitName');
        const status = urlParams.get('status');
        const segmentId = urlParams.get('segmentId');

        if (unitName) {
            urlQueryParams.unitName = unitName;
        }
        if (status) {
            urlQueryParams.status = status;
        }
        if (segmentId) {
            urlQueryParams.segmentId = segmentId;
        }

        // 合并URL参数和表格参数
        const searchParams = {
            ...params,
            ...urlQueryParams
        };

        return this.api.loadEnforcementNumberTable(searchParams, sort, filter);
    }



    // 加载单位选项到state中
    public loadUnitOptions = async () => {
        try {
            if (this.api.getUnitOptions) {
                const res = await this.api.getUnitOptions();
                this.dispatch((prevState) => ({
                    ...prevState,
                    unitOptions: res.data || []
                }));
            }
        } catch (error) {
            console.error('获取单位选项失败:', error);
        }
    }

    // 获取单位选项（用于表格列配置）
    public getUnitOptions = async () => {
        try {
            if (this.api.getUnitOptions) {
                const res = await this.api.getUnitOptions();
                return res.data || [];
            }
            return [];
        } catch (error) {
            console.error('获取单位选项失败:', error);
            return [];
        }
    }
}
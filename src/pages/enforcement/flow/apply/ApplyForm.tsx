import React, {useContext, useEffect} from "react";
import {FlowFormViewProps, ValidateUtils} from "@/ui-framework";
import {FormInput, FormSelect, FormTextArea, FormUploader} from "@/form-pc";
import {FlowViewReactContext} from "@/flow-pc";
import {Avatar, message} from "@/components-pc";
import {FlowHandleView} from "@/flow-pc/Flow/components/FlowHandleView";
import {TableForm} from "@/form-pc";
import {Space} from "@/components-pc";


const EnforceApplyForm: React.FC<FlowFormViewProps> = (props) => {

    useEffect(() => {
        if (props.dataVersion && props.data) {
            props.form?.setFieldsValue({
                ...props.data
            });
        }
    }, [props.dataVersion]);

    console.log("apply form values:", props.data)

    const flowViewContext = useContext(FlowViewReactContext);

    flowViewContext?.flowButtonClickContext.setButtonPreHandle({
        beforeSubmit: async (context) => {
            // 处理业务逻辑。如果是发起的表单中点击提交，此时还取不到recordId,因为整个按钮的逻辑都还没走
            const result = await props.form.validate();
            console.log("result", result)
            if (!result) {
                return false;
            }
            if (context.button.name === "提交合法审核") {
                props.form.setFieldValue(["to_legal_review"], true);
                props.form.setFieldValue(["to_dept_leader"], false);
            } else if (context.button.name === "提交部门负责人") {
                props.form.setFieldValue(["to_legal_review"], false);
                props.form.setFieldValue(["to_dept_leader"], true);
            }

            // 可以在这里触发一些别的逻辑， 或者调用自己的接口
            // flowViewContext?.flowEventContext.saveFlow() 保存流程数据
            // props.form.getFieldsValue() 获取表单内容
            return true
        },
        beforeReject: async (context) => {
            message.error("功能测试，暂不支持驳回")
            return false
        }
    });

    return (
            <FlowHandleView
                setAdvice={() => {
                    return props.form.getFieldValue("advice") ?? ""
                }}
                formPage={<div>
                    <TableForm
                        pageHeader={{
                            title: "执法证申领流程",
                            left: "XXX部门，张三",
                            right: "2023-10-01 12:00:00",
                        }}
                        tableHeader={{
                            title: "基本信息",
                        }}
                        formInstance={props.form}>

                        {/* 隐藏字段 */}
                        <TableForm.Item name={['to_legal_review']} hidden={true}>
                            <FormInput/>
                        </TableForm.Item>
                        <TableForm.Item name={['to_dept_leader']} hidden={true}>
                            <FormInput/>
                        </TableForm.Item>
                        <TableForm.Item name={['id']} hidden={true}>
                            <FormInput/>
                        </TableForm.Item>
                        <TableForm.Item name={['username']} hidden={true}>
                            <FormInput/>
                        </TableForm.Item>

                        {/* 基本信息 */}
                        <TableForm.Item label={"姓名"} name={['name']} span={12}>
                            <FormSelect options={[{value: "1", label: "张三"}, {value: "2", label: "李四"}]}/>
                        </TableForm.Item>
                        <TableForm.Item label={"性别"} name={['sex']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"年龄"} name={['age']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"出生日期"} name={['birthday']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"政治面貌"} name={['political_outlook']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"身份证号"} name={['id_number']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"学历"} name={['education']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"专业"} name={['major']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"现职务"} name={['current_position']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"手机号"} name={['phone']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"原职务"} name={['former_position']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"是否正式员工"} name={['is_formal_employee']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"工作单位"} name={['work_unit']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"工作部门"} name={['work_department']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"参与工作时间"} name={['work_start_time']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"是否调离专卖"} name={['is_transferred_from_monopoly']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"从事专卖时间"} name={['monopoly_work_time']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"是否受过处分"} name={['is_disciplined']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"工作简历"} name={['work_experience']} span={24}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                    </TableForm>

                    <TableForm
                        tableHeader={{
                            title: "执法证信息",
                        }}
                        formInstance={props.form}>
                        {/* 执法证信息 */}
                        <TableForm.Item label={"执法证编号"} name={['enforce_cert_number']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"申请时间"} name={['apply_time']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"是否通过资格考试"} name={['is_qualified_exam_passed']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>
                        <TableForm.Item label={"备注"} name={['remark']} span={12}>
                            <FormInput disabled={true}/>
                        </TableForm.Item>

                        {/* 审批意见 */}
                        <TableForm.Item
                            name={"advice"}
                            label={"审批意见"}
                            required={true}
                            span={24}
                        >
                            <FormTextArea
                                textAreaRows={3}
                                inputType={"number"}
                                validateFunction={ValidateUtils.validateNotEmpty}/>
                        </TableForm.Item>
                    </TableForm>
                    <FormUploader />
                </div>}
            />
    )
}

export default EnforceApplyForm;
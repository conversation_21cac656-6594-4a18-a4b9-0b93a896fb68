export type Dispatch<T> = (updater: ((prevState: T) => T) | T) => void;

export interface EnforcementCertficateState {
    enforcementCertficateEditVisible:boolean,
    enforcementCertficateAddVisible:boolean,
    currentEditEnforcementCertficate:any,
    checkedEnforcementCertficateIds:number[],
    //刷新版本号(用于钩子监听)
    tableRefreshVersion:number,
    //选中的行数据
    selectedRowKeys:number[],
    selectedRows:any[],

}
export interface EnforcementCertficateApi {

    loadEnforcementCertficateTable: (params: any, sort: any, filter: any) => Promise<any>;

}
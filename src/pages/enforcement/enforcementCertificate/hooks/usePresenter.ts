import {EnforcementCertficateState} from "@/pages/enforcement/enforcementCertificate/types";
import React from "react";
import {EnforcementCertficatePresenter} from "@/pages/enforcement/enforcementCertificate/presenter";
import {EnforcementCertficateApiImpl} from "@/pages/enforcement/enforcementCertificate/model";

const initialState: EnforcementCertficateState = {
    enforcementCertficateAddVisible:false,
    enforcementCertficateEditVisible:false,
    currentEditEnforcementCertficate:null,
    checkedEnforcementCertficateIds:[],
    tableRefreshVersion:0,
    selectedRowKeys:[],
    selectedRows:[]
}

export  function useEnforcementCertficatePresenter(){
    const [state, dispatch] = React.useState<EnforcementCertficateState>(initialState);

    const presenterRef = React.useRef<EnforcementCertficatePresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new EnforcementCertficatePresenter(dispatch, new EnforcementCertficateApiImpl(), state);
    }
    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    return {
        state,
        presenter: presenterRef.current,
    }
}
import {ActionType, PageContainer, ProColumns} from "@ant-design/pro-components";
import React from "react";
import {ProTable, Button} from "@/components-pc";
import {Form, InputNumber, Modal, Popconfirm, Select, Input, message} from "antd";
import {useEnforcementCertficatePresenter} from "@/pages/enforcement/enforcementCertificate/hooks/usePresenter";

const EnforcementCertficateManagePage: React.FC = () => {
    const actionRef = React.useRef<ActionType>(null);
    const [form] = Form.useForm();
    // 通过hooks获取state状态数据与presenter对象
    const {state, presenter} = useEnforcementCertficatePresenter();
    //通过钩子函数来更改编辑表单的回显值
    React.useEffect(() => {
        if (state.currentEditEnforcementCertficate){
            form.setFieldsValue(state.currentEditEnforcementCertficate)
        }
    },[state.currentEditEnforcementCertficate])
    const columns: ProColumns<any>[] = [
        {
            title: "编号",
            dataIndex: "id",
            search: false,
        },
        {
            title: "单位名称",
            dataIndex: "unitName",
        },
        {
            title: "单位ID",
            dataIndex: "unitId",
            search: false,
            hidden: true
        },
        {
            title: "执法编号",
            dataIndex: "enforcementNumber",
            search: false,
        },
        {
            title: "业务状态",
            dataIndex: "businessStatus",
            search: false,
            valueType: "select",
            valueEnum: {
                "申请中": { text: "申请中", status: "Processing" },
                "已发证": { text: "正常", status: "Success" },
                "已过期": { text: "已过期", status: "Error" },
            },
        },
        {
            title: "证件状态",
            dataIndex: "physicalStatus",
            search: false,
            valueType: "select",
            valueEnum: {
                "不存在": { text: "不存在", status: "Processing" },
                "正常": { text: "正常", status: "Success" },
                "收回": { text: "收回", status: "Default" },
                "待收回": { text: "待收回", status: "Warning" },
                "丢失": { text: "丢失", status: "Error" },
                "暂扣": { text: "暂扣", status: "Warning" },
                "遗失": { text: "遗失", status: "Default" },
                "销毁": { text: "销毁", status: "Default" }
            },
        },
        {
            title: "用户ID",
            dataIndex: "userId",
            search: false,
            hidden: true
        },
        {
          title: "证件持有者",
          dataIndex: "userName",
          search: false,
        },
        {
            title: "申请时间",
            dataIndex: "applicationTime",
            search: false,
            valueType: "dateTime",
            width: 160,
        },
        {
            title: "发证时间",
            dataIndex: "issueTime",
            search: false,
            valueType: "dateTime",
            width: 160,
        },
        {
            title: "有效期开始时间",
            dataIndex: "validFromTime",
            search: false,
            valueType: "date",
            width: 120,
        },
        {
            title: "有效期结束时间",
            dataIndex: "validToTime",
            search: false,
            valueType: "date",
            width: 120,
            render: (_, record) => {
                if (!record.validToTime) return '-';
                const endDate = new Date(record.validToTime);
                const now = new Date();
                const isExpired = endDate < now;
                const daysLeft = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

                return (
                    <span style={{ color: isExpired ? 'red' : daysLeft < 30 ? 'orange' : 'inherit' }}>
                        {record.validToTime.split(' ')[0]}
                        {isExpired && ' (已过期)'}
                        {!isExpired && daysLeft < 30 && ` (${daysLeft}天后过期)`}
                    </span>
                );
            },
        },
        {
            title: "操作",
            key: 'option',
            valueType: "option",
            width: 180,
            render: (_, record) => [
                <a
                    key="update"
                    onClick={() => {
                        // presenter.showValueEditModal(record.code);
                    }}
                >编辑</a>,
            ],
        },
    ];
    // @ts-ignore
    return (
        <PageContainer>
            <ProTable
            columns={columns}
            actionRef={actionRef}
            rowKey={"id"}
            toolBarRender={() => [
                <Button
                    key="add"
                    type="primary"
                    onClick={() => {
                        presenter.showEnforcementCertficateAddModal();
                    }}
                >
                    新增执法证
                </Button>
            ]}
            request={async (params, sort, filter) => {
                  return  presenter.searchEnforcementCertficateTable(params, sort, filter)
            }}
            />
            <Modal
                title={state.enforcementCertficateAddVisible ? "新增执法证" : "编辑执法证"}
                open={state.enforcementCertficateAddVisible||state.enforcementCertficateEditVisible}
                onCancel={() => {
                    if (state.enforcementCertficateEditVisible) {
                        presenter.hideEnforcementCertficateEditModal()
                    }else {
                        presenter.hideEnforcementCertficateAddModal();
                    }
                }}
            >
                <Form
                    form={form}
                    labelCol={{ span: 4 }}
                    wrapperCol={{ span: 14 }}
                    layout="horizontal"
                    style={{ maxWidth: 600 }}
                >
                    <Form.Item
                        label="单位名称"
                        name="unitName"
                        rules={[{ required: true, message: "请选择单位名称" }]}
                    >
                        <Select
                            placeholder="请选择单位名称"
                            options={[
                                { value: "unit1", label: "单位一" },
                                { value: "unit2", label: "单位二" },
                                { value: "unit3", label: "单位三" },
                            ]}
                        />
                    </Form.Item>
                    <Form.Item
                        label="起始编号"
                        name="startNumber"
                        rules={[{ required: true, message: "请输入起始编号" }]}
                    >
                        <InputNumber
                            placeholder="请输入起始编号"
                            style={{ width: "100%" }}
                            min={1}
                        />
                    </Form.Item>
                    <Form.Item
                        label="结束编号"
                        name="endNumber"
                        rules={[{ required: true, message: "请输入结束编号" }]}
                    >
                        <InputNumber
                            placeholder="请输入结束编号"
                            style={{ width: "100%" }}
                            min={1}
                        />
                    </Form.Item>
                    <Form.Item
                        label="备注"
                        name="remark"
                    >
                        <Input.TextArea
                            placeholder="请输入备注信息"
                            rows={3}
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </PageContainer>
    );
}
export default EnforcementCertficateManagePage;
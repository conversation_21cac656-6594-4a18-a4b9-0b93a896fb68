import {EnforcementCertficateApi} from "@/pages/enforcementCertificate/types";


/**
 * Model层接口实现
 * EnforcementCertficateApiImpl 实现了EnforcementCertficateApi接口，提供了数据的增删改查操作
 */
export class EnforcementCertficateApiImpl implements EnforcementCertficateApi {

    async loadEnforcementCertficateTable(params: any, sort: any, filter: any): Promise<any> {
        // 模拟API延迟
        await new Promise(resolve => setTimeout(resolve, 500));

        // 贵州各市县烟草公司执法证模拟数据
        const mockData = [
            {
                id: 1,
                unitName: "贵州省烟草公司贵阳市公司",
                unitId: "unit001",
                enforcementNumber: "ZF520100001",
                businessStatus: "已发证",
                physicalStatus: "正常",
                userId: "user001",
                applicationTime: "2024-01-10 09:00:00",
                issueTime: "2024-01-15 09:00:00",
                validFromTime: "2024-01-15 00:00:00",
                validToTime: "2026-01-14 23:59:59",
                remark: "贵阳市主城区执法证",
                createTime: "2024-01-15 09:00:00"
            },
            {
                id: 2,
                unitName: "贵州省烟草公司遵义市公司",
                unitId: "unit002",
                enforcementNumber: "ZF520300002",
                businessStatus: "已发证",
                physicalStatus: "正常",
                userId: "user002",
                applicationTime: "2024-01-10 09:15:00",
                issueTime: "2024-01-15 09:15:00",
                validFromTime: "2024-01-15 00:00:00",
                validToTime: "2026-01-14 23:59:59",
                remark: "遵义市红花岗区执法证",
                createTime: "2024-01-15 09:15:00"
            },
            {
                id: 3,
                unitName: "贵州省烟草公司六盘水市公司",
                unitId: "unit003",
                enforcementNumber: "ZF520200003",
                businessStatus: "申请中",
                physicalStatus: "不存在",
                userId: null,
                applicationTime: "2024-01-12 09:30:00",
                issueTime: null,
                validFromTime: null,
                validToTime: null,
                remark: "待分配执法证",
                createTime: "2024-01-15 09:30:00"
            },
            {
                id: 4,
                unitName: "贵州省烟草公司安顺市公司",
                unitId: "unit004",
                enforcementNumber: "ZF520400004",
                businessStatus: "正常",
                physicalStatus: "正常",
                userId: "user004",
                applicationTime: "2024-01-08 09:45:00",
                issueTime: "2024-01-15 09:45:00",
                validFromTime: "2024-01-15 00:00:00",
                validToTime: "2026-01-14 23:59:59",
                remark: "安顺市西秀区执法证",
                createTime: "2024-01-15 09:45:00"
            },
            {
                id: 5,
                unitName: "贵州省烟草公司毕节市公司",
                unitId: "unit005",
                enforcementNumber: "ZF520500005",
                businessStatus: "无效",
                physicalStatus: "销毁",
                userId: "user005",
                applicationTime: "2023-12-01 10:00:00",
                issueTime: "2023-12-15 10:00:00",
                validFromTime: "2023-12-15 00:00:00",
                validToTime: "2024-01-10 23:59:59",
                remark: "已释放，可重新分配",
                createTime: "2024-01-15 10:00:00"
            },
            {
                id: 6,
                unitName: "贵州省烟草公司铜仁市公司",
                certificateNumber: "ZF520600006",
                usageStatus: "1",
                userId: "user006",
                userName: "刘小红",
                remark: "铜仁市碧江区执法证",
                createTime: "2024-01-15 10:15:00"
            },
            {
                id: 7,
                unitName: "贵州省烟草公司黔东南州公司",
                certificateNumber: "ZF522600007",
                usageStatus: "0",
                userId: null,
                userName: null,
                remark: "凯里市新增执法证",
                createTime: "2024-01-15 10:30:00"
            },
            {
                id: 8,
                unitName: "贵州省烟草公司黔南州公司",
                certificateNumber: "ZF522700008",
                usageStatus: "1",
                userId: "user008",
                userName: "杨国庆",
                remark: "都匀市执法证",
                createTime: "2024-01-15 10:45:00"
            }
        ];

        // 模拟搜索过滤
        let filteredData = mockData;

        if (params.unitName) {
            filteredData = filteredData.filter(item =>
                item.unitName.includes(params.unitName)
            );
        }

        if (params.usageStatus !== undefined && params.usageStatus !== '') {
            filteredData = filteredData.filter(item =>
                item.usageStatus === params.usageStatus
            );
        }

        if (params.userName) {
            filteredData = filteredData.filter(item =>
                item.userName && item.userName.includes(params.userName)
            );
        }

        // 模拟分页
        const pageSize = params.pageSize || 10;
        const current = params.current || 1;
        const total = filteredData.length;
        const startIndex = (current - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const data = filteredData.slice(startIndex, endIndex);

        return {
            data,
            total,
            success: true,
            pageSize,
            current
        };
    }
}
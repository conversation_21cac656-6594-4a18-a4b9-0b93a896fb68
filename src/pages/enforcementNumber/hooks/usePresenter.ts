import {EnforcementNumberState} from "@/pages/enforcementNumber/types";
import React from "react";
import {EnforcementNumberPresenter} from "@/pages/enforcementNumber/presenter";
import {EnforcementNumberApiImpl} from "@/pages/enforcementNumber/model";
import {useLocation} from "react-router-dom";

const initialState: EnforcementNumberState = {
    enforcementNumberAddVisible: false,
    enforcementNumberEditVisible: false,
    currentEditEnforcementNumber: null,
    checkedEnforcementNumberIds: [],
    tableRefreshVersion: 0,
    selectedRowKeys: [],
    selectedRows: [],
    statusOptions: [],
    formInitialValues: {},
    unitOptions: []
}

export  function useEnforcementNumberPresenter(){
    const [state, dispatch] = React.useState<EnforcementNumberState>(initialState);
    const location = useLocation();

    const presenterRef = React.useRef<EnforcementNumberPresenter | null>(null);

    // 如果 presenterRef.current 为空，则创建一个新的 DictPresenter 实例
    if (!presenterRef.current) {
        presenterRef.current = new EnforcementNumberPresenter(dispatch, new EnforcementNumberApiImpl(), state);
    }

    // 当 state 发生变化时，更新 presenter 的状态
    React.useEffect(() => {
        presenterRef.current?.syncState(state);
    }, [state])

    // 初始化时解析URL参数，监听location变化
    React.useEffect(() => {
        presenterRef.current?.initializeFromUrlParams();
    }, [location.search]);

    return {
        state,
        presenter: presenterRef.current,
    }
}
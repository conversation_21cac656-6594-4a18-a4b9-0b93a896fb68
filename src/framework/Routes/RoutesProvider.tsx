/*
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-26 10:10:35
 * @Description: 应用路由配置中心
 * @LastEditTime: 2025-07-23 10:29:10
 * @LastEditors: wang<PERSON><PERSON><PERSON>
 */
import React, { lazy, ReactNode, Suspense } from "react";
import {
  createHashRouter,
  RouterProvider,
  redirect,
  RouteObject, // 添加redirect导入
} from "react-router-dom";
import Layout from "@/layout";
import Login from "@/pages/login";
import NotFound from "@/layout/NotFound";
import { useMenuStore } from "@/store";
import World from "@/pages/template/pageoffice/word.js";
import InstitutionDraftWord from "@/pages/institution/draft/pageoffice/word.js";
import ContractDraft from "@/pages/contract/draft";
import ContractRenewal from "@/pages/contract/renewal/draft";
import FlowPageOffice from "@/pages/contract/flow-pageOffice";
import ContractUpdate from "@/pages/contract/update";
import Test from "@/pages/Test";
import PageTitle from "@/components/pageTitle";
import AbolishDetail from "@/pages/abolish/detail";
import TeamBuilding from "@/pages/Test/TeamBuilding";
import LegalAffairs from "@/pages/Test/LegalAffairs";
import Home from "@/pages/Test/Home";
import RegulationsRulesDatabase from "@/pages/Test/DataBase";
import GeneralManage from "@/pages/Test/GeneralManage";
import LawsRegulations from "@/pages/Test/LawsRegulations";
import ContentCompare from "@/pages/Test/ContentCompare";
import AssessDetail from "@/pages/assess/detail";
import Dashboard from "@/pages/Danshboard";
import OpinionTable from "src/pages/institution/opinionSummery"
// 模拟认证状态 - 实际项目中应该从全局状态获取
const isAuthenticated = () => {
  return !!localStorage.getItem("token"); // 或其他认证状态检查
};

// 登录路由加载器
const loginLoader = () => {
  if (isAuthenticated()) {
    return redirect("/");
  }
  return null;
};

const RoutesProvider: React.FC = () => {
  const { menus } = useMenuStore();
  /**
   * 递归遍历菜单树，提取所有有效的路由配置
   */
  const extractRoutesFromMenu = (menuItems: any[]): RouteObject[] => {
    const routes: RouteObject[] = [];

    const traverse = (items: any[]) => {
      items.forEach((item) => {
        if (item.url && item.type === "APP") {
          // 规范化路径，确保以斜杠开头且不重复斜杠
          let routePath = item.url.startsWith("/") ? item.url : `/${item.url}`;
          routePath = routePath.replace(/\/+/g, "/"); // 去除重复斜杠

          // 处理动态导入路径
          let importPath = routePath;
          // 特殊路径处理（根据需要调整）
          if (routePath === "/contractTemplateDatabase") {
            importPath = "/institutionDatabase"; // 示例调整
          }

          let LazyComponent: any;
          try {
            LazyComponent = lazy(() =>
              import(`@/pages${importPath}`).catch((error) => {
                console.error(`加载页面 ${importPath} 失败:`, error);
                return { default: () => <NotFound /> };
              })
            );
          } catch (e) {
            console.error(`创建懒加载组件失败:`, e);
            LazyComponent = () => <NotFound />;
          }

          routes.push({
            path: routePath,
            element: (
              <Suspense fallback={<div>加载中...</div>}>
                <PageTitle title={item.title} component={<LazyComponent />} />
              </Suspense>
            ),
          });
        }

        if (item.children?.length) {
          traverse(item.children);
        }
      });
    };

    traverse(menuItems);
    return routes;
  };

  // 路由配置数组
  const routes: RouteObject[] = [
    {
      element: <PageTitle title="首页" component={<Layout />} />,
      errorElement: <NotFound />,
      children: [
        {
          index: true, // 这是根路径/的默认子路由
          element: <Home />, // 当访问/时渲染的组件
        },
        {
          path: "regulations-rules-database", // 注意这里不需要前导斜线
          element: <RegulationsRulesDatabase />,
        },
        {
          path: "general-manage",
          element: <GeneralManage />,
        },
         {
          path: "laws-regulations",
          element: <LawsRegulations />,
        },
      ],
    },
    {
      path: "/login",
      element: <PageTitle title="登录" component={<Login />} />,
      loader: loginLoader, // 添加登录路由的loader
    },
    {
      path: "/template/word",
      element: <World />,
    },
    {
      path: "/institution/draft/pageoffice/word",
      element: <InstitutionDraftWord />,
    },
    ...extractRoutesFromMenu(menus),
    {
      path: "/contact-draft-page-office",
      element: <ContractDraft />,
    },
    {
      path: "/contact-renewal-page-office",
      element: <ContractRenewal />,
    },
    {
      path: "/flow-page-office",
      element: <FlowPageOffice />,
    },
    {
      path: "/contract/update",
      element: <ContractUpdate />,
    },
    {
      path: "/abolish/detail",
      element:<PageTitle title="制度废止详情" component={<AbolishDetail/>} />, 
    },
     {
      path: "/assess/detail",
      element: <PageTitle title="制度评估详情" component={<AssessDetail />} />, 
    },
    {
      path: "/team/building",
      element: <TeamBuilding />,
    },
    {
      path: "/test/content-compare",
      element: <PageTitle title="内容比较" component={<ContentCompare />} />,
    },
    {
      path: "/dashboard",
      element: <Dashboard />,
    },
    {
      path: "*",
      element: <NotFound />,
    },
  ];

  // 创建哈希路由
  const router = createHashRouter(routes);

  return <RouterProvider router={router} />;
};

export default RoutesProvider;

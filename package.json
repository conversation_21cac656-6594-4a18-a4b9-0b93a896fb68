{"name": "admin-ui", "version": "0.1.0", "private": true, "dependencies": {"@ag-grid-community/locale": "^33.0.3", "@ant-design/icons": "^5.4.0", "@ant-design/pro-components": "^2.8.2", "@babel/standalone": "^7.25.6", "@codingapi/ui-framework": "^0.0.41", "@dnd-kit/core": "^6.2.0", "@dnd-kit/sortable": "^9.0.0", "@handsontable/react-wrapper": "^15.0.0", "@itm/components-pc": "0.1.12", "@logicflow/core": "^2.0.5", "@logicflow/extension": "^2.0.9", "@reduxjs/toolkit": "^2.8.2", "ag-grid-react": "^33.0.3", "antd": "^5.25.2", "axios": "^1.7.7", "babel-loader": "^10.0.0", "base64-js": "^1.5.1", "classnames": "^2.5.1", "dayjs": "^1.11.13", "echarts": "^5.6.0", "handsontable": "^15.0.0", "js-pageoffice": "6.5.0", "jszip": "^3.10.1", "lodash": "^4.17.21", "moment": "^2.30.1", "monaco-editor": "^0.52.2", "rc-field-form": "^2.7.0", "rc-select": "^14.16.8", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.0.0", "react-redux": "^9.2.0", "react-router": "^6.26.2", "react-router-dom": "^6.30.1", "react-select": "^5.10.2", "react-to-print": "^3.1.1", "remark-gfm": "^4.0.1", "styled-components": "^6.1.19", "typescript": "^5.6.2", "web-vitals": "^2.1.4", "zustand": "^5.0.5"}, "scripts": {"dev": "rsbuild start --config rsbuild.config.dev.ts", "start": "rsbuild start", "mock": "rsbuild start --config rsbuild.config.mock.ts", "build": "rsbuild build", "test": "jest", "test:watch": "jest --watchAll"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@module-federation/rsbuild-plugin": "^0.17.0", "@rsbuild/core": "^1.4.7", "@rsbuild/plugin-react": "^1.3.4", "@rsbuild/plugin-sass": "^1.3.3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/babel__standalone": "^7.1.7", "@types/classnames": "^2.3.4", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.7", "@types/lodash-es": "^4.17.12", "@types/minimatch": "^5.1.2", "@types/mockjs": "^1.0.10", "@types/node": "^24.0.10", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "babel-jest": "^29.7.0", "css-loader": "^7.1.2", "express": "^4.21.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mockjs": "^1.1.0", "monaco-editor-webpack-plugin": "^7.1.0", "sass": "^1.78.0", "sass-loader": "^16.0.1", "style-loader": "^4.0.0", "ts-jest": "^29.3.2", "ts-loader": "^9.5.1", "ts-node": "^10.9.2"}}
import { defineConfig } from "@rsbuild/core";
import commonConfig from "./rsbuild.config";

export default defineConfig({
  ...commonConfig,
  server: {
    port: 8000,
    proxy: {
      "/itm-user": "http://***********:8290/",
      "/itm-oss": "http://***********:8290/",
      "/itm-archive-oss": "http://***********:8290/",
      "/itm-supplier": "http://***********:8290/",
      "/itm-template": "http://***********:8290/",
      "/itm-collect": "http://***********:8290/",
      "/itm-compliance": "http://***********:8290/",
      "/itm-dict": "http://***********:8290/",
      "/itm-timer": "http://***********:8290/",
      "/api": "http://***********:8290/",
      "/open": "http://***********:8290/",
      "/itm-archive-tag": "http://***********:8290/",
      "/itm-archive-institution": "http://***********:8290/",
      "/itm-legal-review-contract": "http://***********:8290/",
      "/itm-abolish": "http://***********:8290/",
      "/itm-institution-draft": "http://***********:8308/",
      // "/itm-supervise": "http://***********:8290/",
      "/itm-study": "http://***********:8290/",
      "/itm-supervise": "http://localhost:8330/",
    },
  },
});

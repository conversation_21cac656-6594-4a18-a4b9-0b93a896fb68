<!DOCTYPE html>
<html>
<head>
    <title>测试URL参数</title>
</head>
<body>
    <h1>测试URL参数传递</h1>
    <p>点击下面的链接测试不同的URL参数组合：</p>
    
    <ul>
        <li><a href="?unitName=市公安局&status=IN_USE">单位名称=市公安局，状态=使用中</a></li>
        <li><a href="?unitName=交警支队&status=DISABLED">单位名称=交警支队，状态=未启用</a></li>
        <li><a href="?segmentId=123&status=UNUSED">号段ID=123，状态=未使用</a></li>
        <li><a href="?unitName=消防支队&segmentId=456&status=REVOKED">单位名称=消防支队，号段ID=456，状态=作废</a></li>
    </ul>
    
    <h2>当前URL参数：</h2>
    <div id="params"></div>
    
    <script>
        // 显示当前URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const paramsDiv = document.getElementById('params');
        
        if (urlParams.toString()) {
            let paramsList = [];
            for (let [key, value] of urlParams) {
                paramsList.push(`${key}: ${value}`);
            }
            paramsDiv.innerHTML = '<pre>' + paramsList.join('\n') + '</pre>';
        } else {
            paramsDiv.innerHTML = '<em>没有URL参数</em>';
        }
    </script>
</body>
</html>

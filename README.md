# Admin-ui with Antd For Micro Frontends

This is a simple React App with Webpack5 & Typescript.

## Features
1. Support Webpack 5 ModuleFederationPlugin for Micro Frontends
2. Support Dynamic zip component loading
3. Support Dynamic Routing & Dynamic Menu
4. Support Axios for API calls
5. Support Antd & Pro-Components UI Library
6. Support Redux for State Management
7. Support Mock Server for API Mocking
8. Support Monaco Editor for Code Editor
9. Support Access ControlPanel for Menu & Page Components
10. Support Jest for Unit Testing
11. Support DockerCompose for Deployment

## Running
```shell
yarn

yarn start
```
## Build
```shell
yarn build
```

## Deploy
```shell
cd scripts
sh package.sh
sh deploy.sh
```


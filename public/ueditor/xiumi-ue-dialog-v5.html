<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>XIUMI connect</title>
    <style>
        html,
        body {
            padding: 0;
            margin: 0;
        }

        #xiumi {
            position: absolute;
            width: 100%;
            height: 100%;
            border: none;
            box-sizing: border-box;
        }
    </style>
</head>

<body>
    <iframe id="xiumi" src="https://xiumi.us/studio/v5#/paper">
    </iframe>
    <script type="text/javascript" src="dialogs/internal.js"></script>
    <script>
        var xiumi = document.getElementById('xiumi');
        var xiumi_url = "https://xiumi.us";
        console.log("xiumi_url is %o", xiumi_url);
        xiumi.onload = function () {
            console.log("postMessage to %o", xiumi_url);
            // "XIUMI:3rdEditor:Connect" 是特定标识符，不能修改，大小写敏感
            xiumi.contentWindow.postMessage('XIUMI:3rdEditor:Connect', xiumi_url);
        };
        document.addEventListener("mousewheel", function (event) {
            event.preventDefault();
            event.stopPropagation();
        });
        window.addEventListener('message', function (event) {
            console.log("Received message from xiumi, origin: %o %o", event.origin, xiumi_url);
            if (event.origin == xiumi_url) {
                console.log("Inserting html");
                editor.execCommand('insertHtml', event.data);
                console.log("Xiumi dialog is closing");
                dialog.close();
            }
        }, false);
    </script>
</body>

</html>
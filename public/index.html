<!--
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-30 11:36:15
 * @Description: 
 * @LastEditTime: 2025-07-25 15:22:41
 * @LastEditors: wang<PERSON><PERSON>ao
-->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.png" type="image/x-icon" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />

    <title>贵州烟草商业政策法规制度数字化平台</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
    <!-- TODO 临时静态资源地址 -->
    <script
      type="text/javascript"
      src="/itm-legal-review-contract/pageoffice.js"
    ></script>
  </body>
</html>
